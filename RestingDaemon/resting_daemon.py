#!/usr/bin/python3
# -*- coding: utf-8 -*-

"""Create a daemon to execute queries against our Salesforce account.

Using the SalesForce Streaming API, implemented in StreamingClient,
lurk, waiting until signal is received to grab unsigned keylist from SalesForce.

When StreamingClient indicates license should be generated, interrogate SF,
and pass-through unsigned xml to XmlSigner, then return resultant signed license
to SalesForce.

"""
import hashlib
import os
import pprint
import sys
import time
import datetime  # Added for better timestamping in restarts
from typing import Optional

import requests
from requests import HTTPError, RequestException

from CLI_guy import utils
from shared.LicensingConfig import LicensingConfig
from shared.constants import SF_API_VERSION, LicenseState, PATH_TO_CONFIG_FILE, PROJECT_ROOT, PATH_TO_EMP_CONNECTOR_JAR

try:
    import simplejson as json
except ImportError:
    import json

import subprocess
import logging.handlers
import traceback
from threading import Thread
from queue import Queue, Empty

log = logging.getLogger("resting_daemon")
key_record_log = logging.getLogger("KeyRecord")
LICENSING_CONFIG: Optional[LicensingConfig] = None


def load_config():
    global LICENSING_CONFIG
    if LICENSING_CONFIG is None:
        LICENSING_CONFIG = LicensingConfig.from_json_file(PATH_TO_CONFIG_FILE)


load_config()


def check_log_setup(log_loc, log_name):
    # need to check existence of and create any missing/required logs with correct permissions
    # We need to create one log for the licenseSigner process: licenseSigner.log
    if not os.path.isdir(log_loc):
        sys.exit(f"Could not start daemon. Attempted to initialize logging, but directory doesn't exit: {log_loc}")
    open(os.path.join(log_loc, "licenseSigner.log"), "a+")
    open(os.path.join(log_loc, log_name), "a+")


def req_from_sf(url, query=None):
    params = {"q": query} if query else {}
    try:
        response = requests.get(
            url,
            # TODO: all auth bearer? or only custom SF endpoints, the others are Oauth?
            headers={"Authorization": f"Bearer {utils.get_oauth_token(LICENSING_CONFIG)}"},
            params=params,
        )
        return response.json()
    except HTTPError as he:
        log.error(f"HTTP error occurred: {he.response.status_code} - {he.response.text}")
    except RequestException:
        log.error("Unable to read SF query response", exc_info=True)
    except ValueError as ve:
        log.error(f"Failed to parse JSON response", ve)
    return None


def get_signed_key_from_sf(key_id):
    url = f"{LICENSING_CONFIG.login_server}/services/data/v{SF_API_VERSION}/sobjects/LicenseKey__c/{key_id}"
    return req_from_sf(url)


def update_db(updated_values, license_id):
    try:
        with requests.Session() as session:
            response = session.patch(
                f"{LICENSING_CONFIG.login_server}/services/data/v{SF_API_VERSION}/sobjects/LicenseKey__c/{license_id}?_HttpMethod=PATCH",
                json=updated_values,
                timeout=10,
                headers={
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {utils.get_oauth_token(LICENSING_CONFIG)}",
                },
            )
            if response.status_code // 100 == 2:
                log.debug(f"Update for license with ID: {license_id} was successful.")
            else:
                log.warning(f"Update for license with ID: {license_id} was not successful.")
    except HTTPError as e:
        log.error(f"HTTP error occurred: {e.response.status_code} - {e.response.text}")
    except RequestException as e:
        log.error(f"Failed to update SF: {str(e)}")


def query_sf(sf_url, query):
    """Query Salesforce, follow record URLs, and return collected data."""
    all_data = []
    try:
        results = req_from_sf(url=sf_url, query=query)
        records = results.get("records", [])

        for record in records:
            url = record.get("attributes", {}).get("url")
            if url:
                record_data = req_from_sf(url=f'{LICENSING_CONFIG.login_server}{url}')
                all_data.append(record_data)
        return all_data
    except Exception:
        log.error("Error querying SF.", exc_info=True)
        return []


def enqueue_output(std_out, queue):
    for output_line in iter(std_out.readline, b""):
        queue.put(output_line)
    std_out.close()


def output_checksum(license_string, md5_string):
    checksum_path = os.path.join(LICENSING_CONFIG.checksum_loc, md5_string[:2], md5_string)
    log.debug(f"Checksum path: {checksum_path}")
    # check for a dir named for the first two chars of the md5, if it's not there, create it
    if not os.path.exists(os.path.dirname(checksum_path)):
        log.debug(f"Creating new directory - {md5_string[:2]}")
        os.makedirs(os.path.dirname(checksum_path))
    license_dump = open(checksum_path, "w")
    log.debug(f"Writing copy of license keyfile to {checksum_path}")
    license_dump.write(license_string)
    license_dump.close()


def validate_key(unsigned_key_obj):
    required_keys = {"Id", "State__c", "Name", "Unsigned_XML__c"}
    if not isinstance(unsigned_key_obj, dict):
        log.error("Response from SF should be a dict")
        return False

    missing_keys = required_keys - unsigned_key_obj.keys()
    if missing_keys:
        log.error(f"SF response is missing key(s): {''.join(missing_keys)}")
        return False

    unsigned_xml = unsigned_key_obj.get("Unsigned_XML__c")
    if unsigned_xml is None:
        log.warning(
            f"SF response for key: {str(unsigned_key_obj.get('Id'))} has no unsigned XML. Will not attempt to sign."
        )
        return False
    return True


def safe_process_unsigned_key(unsigned_key):
    try:
        process_unsigned_key(unsigned_key)
    except Exception as e:
        log.error(f"An error was encountered attempting to sign key:\n{pprint.pformat(unsigned_key)}", e)


def process_unsigned_key(unsigned_key_obj):
    """Validates unsigned XML. Attempts to sign. Writes checksum to disk."""
    if not validate_key(unsigned_key_obj):
        return
    license_id = str(unsigned_key_obj.get("Id", ""))
    key_name = str(unsigned_key_obj.get("Name", ""))
    unsigned_xml = unsigned_key_obj.get("Unsigned_XML__c")
    log.info(
        f'Retrieved unsigned XML from SF License - Key: {key_name}, ID: {license_id}, State: {str(unsigned_key_obj.get("State__c", ""))}'
    )

    signed_key = utils.sign_keylist(unsigned_xml)
    if not signed_key:
        log.error("Nothing returned from signer")
        return
    log.info(f"Key with ID {license_id} was processed and signed.")
    update_db({"Signed_XML__c": signed_key, "State__c": LicenseState.SIGNED_KEY_UPDATED}, license_id)

    # Generate and log checksum
    md5_string = hashlib.md5(signed_key.encode("utf-8")).hexdigest()
    key_record_log.info(f"{key_name},{license_id},Signed Key Updated,{md5_string}")
    output_checksum(signed_key, md5_string)


def process_stream_message(stream_msg):
    stream_event_msg = str(stream_msg)
    if "Awaiting Signed Key" in stream_event_msg:
        # SF located an event, parse the JSON data inside of it
        query_data = utils.convert_emp_string_to_json(stream_event_msg)
        license_key_id = query_data.get("sobject", {}).get("Id")
        if not license_key_id:
            log.warning(f"Expected Id in stream event message: {stream_event_msg}")
            return
        unsigned_keys = query_sf(
            f"{LICENSING_CONFIG.login_server}/services/data/v{SF_API_VERSION}/query",
            f"SELECT Unsigned_XML__c FROM LicenseKey__c WHERE Id='{license_key_id}' "
            f"AND State__c = '{LicenseState.VALIDATED_AWAITING_SIG}'",
        )
        if len(unsigned_keys) != 1:
            error = (
                f"Query for license with id {license_key_id} returned 0 results."
                if len(unsigned_keys) == 0
                else f"Query for license with id {license_key_id} returned {str(len(unsigned_keys))} results."
            )
            update_db({"State__c": LicenseState.ERR_INVALID_SUPPORTING_DATA, "Error_Detail__c": error}, license_key_id)
            return

        # FIX: Changed from list_of_unsigned_keys to unsigned_keys
        for unsigned_key in unsigned_keys:
            safe_process_unsigned_key(unsigned_key)


def start_streaming_api(url, token, max_heap_size=128):
    if not os.path.exists(PATH_TO_EMP_CONNECTOR_JAR):
        raise Exception(f"EMP Connector jar missing. Should be located at {PATH_TO_EMP_CONNECTOR_JAR}")

    log.info("Starting EMP-Connector")

    listener_args = [
        "java",
        f"-Xmx{max_heap_size}m",
        "-classpath",
        os.path.normpath(PATH_TO_EMP_CONNECTOR_JAR),
        "com.salesforce.emp.connector.example.BearerTokenExample",
        url,
        token,
        "/topic/UnsignedXML",
    ]
    on_posix = "posix" in sys.builtin_module_names
    return subprocess.Popen(
        listener_args, stdout=subprocess.PIPE, stderr=subprocess.STDOUT, bufsize=1, text=True, close_fds=on_posix
    )


# Keep global for listener and queue as they are managed across restarts
listener = None
q = None
output_thread = None


def restart_listener(queue, restart_type="unknown"):
    """Terminates existing listener and starts a new one, clearing the queue."""
    global listener, output_thread  # Declare as global to modify

    log.info(f"Restarting StreamingAPI Listener (reason: {restart_type})")

    if listener and listener.poll() is None:  # Check if listener is still running
        try:
            # Prefer terminate, then kill if it doesn't respond
            listener.terminate()
            time.sleep(1)  # Give it a moment to terminate
            if listener.poll() is None:  # Still running?
                listener.kill()
                time.sleep(5)  # Give it more time
        except Exception as e:
            log.debug(f"Error terminating old listener process. {type(e).__name__}: {e}")

    # Clear the queue to prevent memory leaks from accumulated messages
    with queue.mutex:
        queue.queue.clear()
        log.debug("Queue cleared to prevent memory leak.")

    # Start new listener
    listener = start_streaming_api(LICENSING_CONFIG.login_server, utils.get_oauth_token(LICENSING_CONFIG))
    log.debug(f"New Listener PID: {listener.pid}")

    # Start new output thread for the new listener's stdout
    output_thread = Thread(target=enqueue_output, args=(listener.stdout, queue))
    output_thread.daemon = True
    output_thread.start()


class DaemonState:
    """Class to encapsulate and manage the daemon's state variables."""

    def __init__(self):
        self.time_last_hb = time.time()
        self.max_hb_interval = 3 * 60.0  # 3 minutes - hb normally occurs every 2 minutes
        self.max_restarts = 3  # how many restarts result in escalation and email notification
        self.restart_times = []
        self.elevated = False  # keep track of when email has been sent
        self.cur_wait_tm = 0  # time in seconds we wait
        self.resume_time = 0  # seconds since epoch to resume checking
        self.missedSigFreq = 0.5 * 60.0  # 30 seconds
        self.healthySubprocCheck = 30.0  # 30 sec
        self.loop_iteration = 0  # Replaces 'i'

    def record_restart(self):
        self.restart_times.append(time.time())
        self.loop_iteration = 0  # Reset loop iteration after restart

    def check_escalation(self):
        now = time.time()
        recent_restarts_count = 0
        total_time_deltas = 0
        threshold_minutes = 10.0  # 10 minutes ago
        threshold_time = now - threshold_minutes * 60

        # Filter out old restarts and count recent ones
        self.restart_times = [t for t in self.restart_times if t >= threshold_time]
        recent_restarts_count = len(self.restart_times)

        if recent_restarts_count >= self.max_restarts:
            # Calculate average time between restarts for recent ones
            if recent_restarts_count > 1:
                # Sort to ensure correct delta calculation if not already sorted
                self.restart_times.sort()
                for k in range(recent_restarts_count - 1):
                    total_time_deltas += self.restart_times[k + 1] - self.restart_times[k]
                avg_restarts_sec = total_time_deltas / (recent_restarts_count - 1)
            else:  # Only one recent restart, cannot calculate average
                avg_restarts_sec = 0

            contact_email = get_contact_email()
            subject = "SalesForce License Signer problem"
            message = ""

            if not self.elevated:
                self.cur_wait_tm = 5  # 5 minutes
                message = (
                    f"{self.max_restarts} restarts have occurred in the last {threshold_minutes} minutes"
                    f"{f' - average time between restarts {avg_restarts_sec:.2f} sec' if avg_restarts_sec > 0 else ''}.\n"
                    f"Will resume checking again in {self.cur_wait_tm} minutes."
                )
                self.elevated = True
            else:
                self.cur_wait_tm = 30  # 30 minutes
                message = (
                    f"After waiting, another {self.max_restarts} restarts have occurred in less than a "
                    f"{threshold_minutes} min period{f' - average time between restarts {avg_restarts_sec:.2f} sec' if avg_restarts_sec > 0 else ''}.\n"
                    f"Will resume checking again in {self.cur_wait_tm} minutes."
                )

            self.resume_time = now + self.cur_wait_tm * 60.0
            self.restart_times = []  # Reset restart times after escalation
            log.debug(f"Notifying {contact_email} via email: {message}")
            utils.send_email(contact_email, subject, message)
        elif self.elevated and recent_restarts_count == 0 and now > self.resume_time:
            # Reset elevated state if healthy for threshold_minutes
            self.elevated = False
            self.resume_time = 0
            contact_email = get_contact_email()
            log.debug(f"Notifying {contact_email} via email that process is healthy")
            subject = "SalesForce License Signer out of trouble"
            message = (
                f"Healthy heartbeats have been received for {threshold_minutes} minutes - "
                "(we assume) everything is back to normal."
            )
            utils.send_email(contact_email, subject, message)

    def should_run_health_check(self):
        now = time.time()
        if self.elevated:
            return now > self.resume_time
        return True


def get_contact_email():
    """Helper to get contact email, with fallback."""
    try:
        return LICENSING_CONFIG.username
    except Exception:
        log.debug(
            f"Could not pull email address from username in {PATH_TO_CONFIG_FILE}, "
            "using <EMAIL>"
        )
        return "<EMAIL>"


def perform_straggler_check():
    """Queries SF for unsigned keys that might have been missed."""
    log.debug(
        "Performing straggler query to verify no licenses are waiting/were missed by StreamingAPI."
    )
    list_of_unsigned_keys = query_sf(
        f"{LICENSING_CONFIG.login_server}/services/data/v{SF_API_VERSION}/query",
        f"SELECT Unsigned_XML__c, Id FROM LicenseKey__c WHERE State__c='{LicenseState.VALIDATED_AWAITING_SIG}'",
    )
    for unsigned in list_of_unsigned_keys:
        safe_process_unsigned_key(unsigned)


if __name__ == "__main__":
    load_config()
    utils.setup_logger("resting_daemon", LICENSING_CONFIG.log_name, LICENSING_CONFIG)
    check_log_setup(LICENSING_CONFIG.log_loc, LICENSING_CONFIG.log_name)

    log.info("Resting Daemon Started")
    log.debug(f"Configuration information:\n {pprint.pformat(LICENSING_CONFIG)}")

    # Initialize state variables
    daemon_state = DaemonState()
    checkFreq = 0.5  # Same as original

    # Setup listener and queue initially
    q = Queue()
    restart_listener(q, "initial_start")  # Initial start

    pid = listener.pid
    log.debug(f"Initial Listener PID: {str(pid)}")

    try:
        while True:
            # 1. Check for placeholder file
            if not os.path.isfile(os.path.join(PROJECT_ROOT, "RestingDaemon", "placeholder.file")):
                if daemon_state.loop_iteration <= 1:
                    log.info(
                        f"In order to run, this program expects "
                        f'\'{os.path.join(PROJECT_ROOT, "RestingDaemon", "placeholder.file")}\' to be present.'
                    )
                else:
                    log.debug("Placeholder file not present. Stopping.")
                break

            # 2. Process messages from the queue
            msg = ""
            try:
                line = q.get_nowait()
                if isinstance(line, (bytes, bytearray)):
                    msg = line.decode().strip()
                else:
                    msg = line.strip()
                if msg:
                    process_stream_message(msg)
            except Empty:
                pass  # No message, continue loop

            # 3. Handle heartbeat and connection renewal
            if ("channel=/meta/subscribe" in msg or "channel=/meta/connect" in msg) and "successful=true" in msg:
                msg_dict = {
                    key.strip(): value.strip()
                    for entry in msg.strip("{}").split(",")
                    if "=" in entry
                    for key, value in [entry.split("=", 1)]
                }

                # StreamingAPI connections are restricted to 120 minutes...at hb 60, restart
                # TODO: remove this restart if this restriction is lifted in future releases of SF StreamingAPI
                hb_limit = 60
                if "id" in msg_dict and int(msg_dict.get("id")) >= hb_limit:
                    log.debug(f'Heartbeat #{msg_dict.get("id")}, restarting to refresh StreamingAPI connection')
                    restart_listener(q, "heartbeat_limit_reached")
                    daemon_state.time_last_hb = time.time()  # Reset HB time after restart
                    daemon_state.loop_iteration = 0  # Reset loop iteration as listener just restarted
                    continue  # Skip remaining checks in this iteration, start fresh

                daemon_state.time_last_hb = time.time()  # Update last heartbeat time
                # if we receive a heartbeat, start checking health again immediately if elevated
                if daemon_state.elevated:
                    log.debug("Heartbeat detected, resuming heartbeat check now")
                    daemon_state.resume_time = 0  # Reset resume time immediately

            # 4. Perform health checks for the listener subprocess
            # Check every 'healthySubprocCheck' seconds based on loop_iteration
            if daemon_state.loop_iteration % (daemon_state.healthySubprocCheck / checkFreq) == 0:
                now = time.time()

                if daemon_state.should_run_health_check():
                    # Check if last heartbeat was too long ago
                    if now - daemon_state.time_last_hb > daemon_state.max_hb_interval:
                        log.error(
                            f"No heartbeat received from SalesForce connection in over {daemon_state.max_hb_interval} seconds."
                        )
                        daemon_state.time_last_hb = time.time()  # Reset heartbeat time to prevent immediate re-trigger
                        restart_listener(q, "no_heartbeat")
                        daemon_state.record_restart()
                        continue  # Restart loop for fresh start

                    # Check if listener process is still alive
                    if listener.poll() is not None:  # listener.poll() returns non-None if process has died
                        log.warning(f"Status poll of StreamingAPI indicates status '{listener.poll()}' - not alive.")
                        restart_listener(q, "listener_died")
                        daemon_state.record_restart()
                        continue  # Restart loop for fresh start

                # Check for escalation after health checks
                daemon_state.check_escalation()

            # 5. Perform straggler checks (missed licenses)
            # Check every 'missedSigFreq' seconds based on loop_iteration
            if daemon_state.loop_iteration % (daemon_state.missedSigFreq / checkFreq) == 0:
                perform_straggler_check()

            # 6. Increment loop counter and sleep
            time.sleep(checkFreq)
            daemon_state.loop_iteration += 1

    except Exception as e:
        log.error(f"Stopping because of error - {traceback.format_exc().strip()}")
    finally:
        # Final cleanup attempt for the listener
        log.info("License Signer terminating subprocesses")
        time.sleep(1)  # Give a moment for any last logging/processing
        if listener and listener.poll() is None:  # Only try to terminate if it's still running
            try:
                listener.terminate()
            except Exception as e:
                # Log this, but don't exit if it fails - might already be dead.
                log.debug(f"Error during final termination of subprocess. {type(e).__name__}: {e}")
        log.info("Shutdown complete.")
        logging.shutdown()
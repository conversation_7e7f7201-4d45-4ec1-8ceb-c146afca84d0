import json
import logging
import logging.handlers
import os
import smtplib
import subprocess
import xml.etree.ElementTree as ElementTree
import datetime as dt
from email.mime.text import MIMEText

from pathlib import Path

import requests
from requests.exceptions import HTTPError 

from ldap3 import Server, Connection, AUTO_BIND_NO_TLS
from requests import RequestException

from shared.LicensingConfig import LicensingConfig
from shared.constants import (
    LicenseType,
    Scheme,
    PATH_TO_LDAP_CONF_FILE,
    PATH_TO_LDAP_CREDS_FILE,
    LicenseVersion,
    DTF,
    FeatureKey,
    PATH_TO_CONFIG_FILE,
    PROJECT_ROOT,
    SF_API_VERSION,
    LicenseState,
)

log = logging.getLogger("cli_runner")


def get_project_root_directory():
    dirs = os.listdir()
    root_dir = os.getcwd()
    while "CLI_guy" not in dirs and os.getcwd() not in "/":
        path = Path(root_dir)
        root_dir = str(path.parent.resolve())
        dirs = os.listdir(root_dir)
    return root_dir


def load_file(path):
    with open(path, "r") as ldc_file:
        return json.load(ldc_file)


def load_licensing_config():
    return LicensingConfig.from_json_file(PATH_TO_CONFIG_FILE)


def user_entries_to_dict(ldap_users):
    return [
        {key: val.pop() if val else "" for key, val in ldap_user.entry_attributes_as_dict.items()}
        for ldap_user in ldap_users
    ]


def retrieve_ldap_user_info(login):
    ldap_config, ldap_creds = load_ldap_config_creds()
    ldap_entries = get_ldap_entries(ldap_config, ldap_creds, login)
    log.debug(f"LDAP search for {login} yielded {len(ldap_entries)} results:\n{str(get_ldap_names(ldap_entries))}")
    return retrieve_user_info(ldap_entries, login)


def get_ldap_names(ldap_users):
    return [user["sAMAccountName"] for user in ldap_users]


def retrieve_user_info(ldap_users, login, ldap_config_file=PATH_TO_LDAP_CONF_FILE):
    if len(ldap_users) == 1:
        user = ldap_users[0]
        attributes = load_file(ldap_config_file)["attributes"]

        return {"login": login, **{field: str(user[field]) if field in user else None for field in attributes}}

    if not ldap_users:
        msg = "No users located that match login name."
        log.error(msg + " Quitting")
        raise NameError(msg)

    names = get_ldap_names(ldap_users)
    log.debug(f"Ldap search results:\n{ldap_users}")
    if names:
        log.debug(names)

    return None  # No single user found


def load_ldap_config_creds():
    log.info("Getting user info from LDAP")
    try:
        ldap_config = load_file(PATH_TO_LDAP_CONF_FILE)
        ldap_creds = load_file(PATH_TO_LDAP_CREDS_FILE)
        return ldap_config, ldap_creds
    except Exception as e:
        log.error(e)
        raise


def get_ldap_entries(ldap_config, ldap_creds, login=""):
    server = Server(ldap_config["server"], port=389, use_ssl=True)
    with Connection(
        server,
        auto_bind=AUTO_BIND_NO_TLS,
        lazy=True,
        read_only=True,
        user=ldap_creds["dn"],
        password=ldap_creds["pw"],
    ) as c:
        c.search(
            search_base=ldap_config["search_base"],
            search_filter=ldap_config["search_filter"] % login,
            attributes=ldap_config["attributes"],
        )
    return c.entries


def get_license_version_number(version_description: LicenseVersion) -> int:
    version_mapping = {LicenseVersion.V3: 3, LicenseVersion.V4: 4}

    if version_description not in version_mapping:
        raise ValueError(f"Invalid Version {version_description}")

    return version_mapping[version_description]


def get_license_type_from_scheme(scheme: Scheme) -> LicenseType:
    scheme_mapping = {
        Scheme.ADVANCED_SUBSCRIPTION: LicenseType.SUBSCRIPTION,
        Scheme.FULL_PURCHASE: LicenseType.PERPETUAL,
        Scheme.SALES_DEMO: LicenseType.DEMO,
        Scheme.ADVANCED_TRIAL: LicenseType.TRIAL,
        Scheme.BASIC: LicenseType.BASIC,
        Scheme.ALL_KEY: LicenseType.ALLK,
    }

    if scheme not in scheme_mapping:
        raise ValueError(f"Invalid scheme: {scheme}")

    return scheme_mapping[scheme]


def to_date_object(date_string):
    """Create a datetime object from a date string in format YYYY-mm-dd"""
    if isinstance(date_string, dt.datetime):
        return date_string
    else:
        return dt.datetime.strptime(date_string, DTF)


def to_date_string(date_object):
    """Create a string representing a date from a datetime object in format YYYY-mm-dd"""
    return date_object.strftime(DTF)


def to_tstamp(date_string):
    """Convert date strings (in form 2026-02-24) to milliseconds timestamp"""
    return int(to_date_object(date_string).timestamp() * 1000)


def rearrange_features(xml_string):
    # Audio must come before MessageConfirmation, if both are listed Ref: Mantis 0003804
    # update: 5/31/13 adding Resiliency feature, make sure it's last
    root = ElementTree.fromstring(xml_string)
    feature_order = [FeatureKey.AUDIO, FeatureKey.MESSAGE_CONFIRMATION, FeatureKey.RESILIENCY]
    features = root.findall("feature")
    features.sort(key=lambda ft: feature_order.index(ft.get("code", "")))
    # Find the index of the first <param> element
    param_index = next((index for index, elem in enumerate(root) if elem.tag.endswith("param")), len(root))

    for feature in features:
        root.remove(feature)
        root.insert(param_index - 1, feature)

    return ElementTree.tostring(root, encoding="utf-8").decode()


def get_timestamp_from_exp(exp):
    if "/24" in exp:
        hours = float(exp.split("/24")[0])
        return to_tstamp(dt.datetime.now() + dt.timedelta(hours=hours))
    return to_tstamp(dt.datetime.now() + dt.timedelta(days=float(exp)))


def format_xml(xml_str):
    element = ElementTree.XML(xml_str)
    ElementTree.indent(element)
    return ElementTree.tostring(element, encoding="unicode")


def get_oauth_token(licensing_config: LicensingConfig):
    """
    The OAuth response has this structure:
    {
        "access_token": "aY=",
        "scope": "sfap_api",
        "instance_url": "https://singlewire--full.sandbox.my.salesforce.com",
        "id": "https://test.salesforce.com/id/00DWF000000jSmZ2AU/005C000000ArQ3XIAV",
        "token_type": "Bearer",
        "issued_at": "1712689726660",
        "api_instance_url": "https://api.salesforce.com"
    }
    """
    token_url = f"{licensing_config.login_server}/services/oauth2/token"

    try:
        with requests.Session() as session:
            oauth_post = session.post(
                token_url,
                data={
                    "grant_type": "client_credentials",
                    "client_id": licensing_config.client_id,
                    "client_secret": licensing_config.client_secret,
                },
            )
            oauth_login_response = oauth_post.json()
            if "access_token" in oauth_login_response:
                return oauth_login_response.get("access_token")
            raise Exception("SF response does not contain an access token.")
    except HTTPError as e:
        log.error(f"HTTP error occurred: {e.response.status_code} - {e.response.text}")
    except RequestException as e:
        log.error(f"Request failed: {str(e)}")
    except ValueError:
        log.error("Failed to parse JSON response.")


def sign_keylist(unsigned_keylist) -> str:
    path_to_signer_jar = os.path.join(PROJECT_ROOT, "LicenseSigner/target/licensesigner-0.2-SNAPSHOT-shaded.jar")
    if not os.path.exists(path_to_signer_jar):
        raise Exception(f"Signer jar missing. Should be located at {path_to_signer_jar}")

    log.info("Calling license signer jar.")
    signer_args = [
        "java",
        f"-Dlog4j.configuration=file:{os.path.join(PROJECT_ROOT, 'LicenseSigner/src/main/resources/log4j.xml')}",
        "-jar",
        path_to_signer_jar,
        unsigned_keylist,
    ]
    try:
        signer = subprocess.Popen(signer_args, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        data_lines, error_lines = signer.communicate()
        if error_lines != b"":
            log.error(f'Error returned from signer subprocess: {error_lines.decode("utf-8")}')
            return ""
        return data_lines.decode("utf-8")
    except Exception as e:
        log.error("Signer failed.", e)
        return ""


def insert_signed_xml(licensing_config: LicensingConfig, signed_xml: str, license_key_id):
    """Insert signed XML into Salesforce for a given license key ID."""

    url = f"{licensing_config.login_server}/services/data/v{SF_API_VERSION}/sobjects/LicenseKey__c/{license_key_id}"
    log.debug(f"Inserting signed key into SF, license key ID: {license_key_id}")

    request_body = {
        "Signed_XML__c": signed_xml,
        "State__c": LicenseState.SIGNED_KEY_UPDATED,
    }

    try:
        with requests.Session() as session:
            response = session.patch(
                url,
                json=request_body,
                timeout=10,
                headers={
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {get_oauth_token(licensing_config)}",
                },
            )

        return response.status_code

    except HTTPError as e:
        log.error(f"HTTP error occurred: {e.response.status_code} - {e.response.text}")
    except RequestException as e:
        log.error(f"Failed to insert signed xml: {str(e)}")

    return response.status_code


def request_license(licensing_config: LicensingConfig, payload):
    """
    Requests a new license from SalesForce.

    The response has this structure:
    {
        "unsignedXml": "<?xml version..."
        "success": true,
        "licenseKeyId": "a0CWH00000yDl3N2AS",
        "errorMessageUser": null
        "errorMessageFull": null,
    }
    Returns:
        str containing unsigned xml if successful, otherwise empty string

    Raises:
        Exception: If the request fails.
    """
    url = f"{licensing_config.login_server}/services/apexrest/AdvLicenseCreate/requestLicense"

    log.debug(f"Requesting license from {url}")
    log.debug(f"Request Payload: {payload}")

    try:
        with requests.Session() as session:
            response = session.post(
                url,
                data=payload,
                timeout=10,
                headers={
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {get_oauth_token(licensing_config)}",
                },
            )

            sf_response = response.json()
            if isinstance(sf_response, list):
                log.warning(f"Unsuccessful request to create license. Response: {sf_response}")
            return sf_response

    except HTTPError as e:
        error = f"HTTP error occurred: {e.response.status_code} - {e.response.text}"
        log.error(error)
        return {"success": False, "errorMessageFull": error, "errorMessageUser": error}
    except RequestException as e:
        error = f"Failed to request new license from SF: {str(e)}"
        log.error(error)
        return {"success": False, "errorMessageFull": error, "errorMessageUser": error}
    except ValueError:
        error = "Failed to parse JSON response."
        log.error(error)
        return {"success": False, "errorMessageFull": error, "errorMessageUser": error}


def send_email(address, subj, body):
    from_address = "<EMAIL>"
    msg = MIMEText(body)
    msg["Subject"] = subj
    msg["From"] = from_address
    msg["To"] = address
    s = smtplib.SMTP("sw-smtp.singlewire.lan")
    s.sendmail(from_address, [address], msg.as_string())
    s.quit()


def setup_logger(name: str, log_filename: str, licensing_config: LicensingConfig):
    """
    Sets up a named logger that writes to a specified file and allows different modules to have separate logs.

    Args:
        name (str): Name of the logger (typically the module name).
        log_filename (str): Name of the log file (e.g., "module.log").
        licensing_config (LicensingConfig): project config containing log config info
    """
    log_level = licensing_config.log_level
    log_path = Path(licensing_config.log_loc).resolve()
    log_path.mkdir(parents=True, exist_ok=True)  # Ensure log directory exists

    log_file = log_path / log_filename

    logger = logging.getLogger(name)
    logger.setLevel(log_level)

    if not logger.hasHandlers():
        # file_handler = logging.FileHandler(log_file)
        file_handler = logging.handlers.RotatingFileHandler(
            log_file, maxBytes=licensing_config.log_size_mb * 1024**2, backupCount=licensing_config.num_logs
        )
        file_handler.setLevel(log_level)

        formatter = logging.Formatter("%(asctime)s - %(levelname)s - [%(name)s] - %(message)s")
        file_handler.setFormatter(formatter)

        logger.addHandler(file_handler)


def convert_emp_string_to_json(stream_string):
    """
    The emp connector returns a json looking string that isn't json. This parses it.
    """
    s = stream_string[2:-1] if stream_string.startswith("b'") else stream_string
    json_string = (
        s.replace("=", '"="')
        .replace('"{', '{"', -1)
        .replace(", ", '","', -1)
        .replace('}"', '"}', -1)
        .replace("}}", '"}}')
        .replace("{", '{"', 1)
        .replace("=", ":", -1)
        .replace('"Validated","Awaiting Signed Key"', '"Validated, Awaiting Signed Key"', 1)
    )
    try:
        return json.loads(json_string)
    except Exception as e:
        log.error("Unable to convert emp string to json", e)
    return json.loads("")

